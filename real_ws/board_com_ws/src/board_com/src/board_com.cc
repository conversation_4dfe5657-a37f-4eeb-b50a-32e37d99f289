#include <iostream>
#include <fstream>
#include <unistd.h>
#include <functional>
#include <regex>
#include <std_msgs/Int8.h>
#include "board_com.h"


#define RFRAME_HEAD	0xFE
#define RFRAME_ADDR	0x55

#define SFRAME_HEAD	0xFE
#define SFRAME_ADDR	0xAA

Eigen::Vector3d ToEulerAngles(Eigen::Quaterniond q);
/**
 * @brief main
 * 
 */
int main(int argc, char *argv[])
{
    ros::init(argc,argv,"boardlink");
    auto node = std::make_shared<LinkComm>();
    ros::Rate loop_rate(1000);
    while(ros::ok())
    {
        node->Serial_GetData();
        node->Link_SendLoop();
        ros::spinOnce();
        loop_rate.sleep();
    }
    return 0;
}

/**
 * @brief Construct a new Link Comm:: Link Comm object
 * 
 */
LinkComm::LinkComm()
{
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        this->hostname_.assign(hostname);
        ROS_INFO("hostname %s", this->hostname_.c_str());
        std::smatch match;
        std::regex regex("\\d+");
        if (std::regex_search(this->hostname_, match, regex)) {
            std::string numberString = match.str();
            this->vswarm_id_ = std::stoi(numberString);
            
            ROS_INFO("VSWARM ID : %d", vswarm_id_);
        } else {
            ROS_INFO("Failed to get id.");
        }
    } else {
        ROS_INFO("Failed to get hostname.");
    }
    /* 参数服务器 */
    ros::param::param<std::string>("~port_name", this->portname_, "/dev/SWARM");
    /* 发布数据 */
    this->pub_imu_ = this->advertise<sensor_msgs::Imu>("robot/imu",10);
    this->pub_battery_ = this->advertise<std_msgs::Int8>("robot/battery",10);
    this->pub_gps_ = this->advertise<sensor_msgs::NavSatFix >("robot/gps",10);
    this->pub_odometry_ = this->advertise<nav_msgs::Odometry>("robot/odom",10);
    /* 速度控制订阅 */
    this->sub_velcmd_ = this->subscribe<geometry_msgs::Twist>("robot/velcmd",10, &LinkComm::VelCmdMsgCallBack, this);
    /* 创建vicon订阅 */
    this->vicon_topic_name_ << "vicon/" << this->hostname_ << "/" << this->hostname_;
    this->sub_vicon_ = this->subscribe<geometry_msgs::TransformStamped>(this->vicon_topic_name_.str(), 10, &LinkComm::ViconMsgCallBack, this);
    this->sub_ledup_ = this->subscribe<std_msgs::UInt32MultiArray>("robot/ledup",10, &LinkComm::LedUpMsgCallBack, this);
    this->sub_leddown_ = this->subscribe<std_msgs::UInt32MultiArray>("robot/leddown",10, &LinkComm::LedDownMsgCallBack, this);

    this->sub_ledcmd_ = this->subscribe<std_msgs::Int8>("robot/ledcmd",10, &LinkComm::LedCmdMsgCallBack, this);

    /* 启停控制客户端 */
    this->ser_sscmd_ = this->advertiseService("sscmd", &LinkComm::StartStopCmdSrvCallBack, this);
    /* 创建timeout */
    serial::Timeout to = serial::Timeout::simpleTimeout(100);
    /* 设置要打开的串口名称 */
    this->serial_.setPort(this->portname_);
    /* 设置串口通信的波特率 */
    this->serial_.setBaudrate(921600);
    /* 串口设置timeout */
    this->serial_.setTimeout(to);
    this->serial_.open();
    /* 协议初始化 */
    Link_Init();
}
/**
 * @brief Destroy the Link Comm:: Link Comm object
 * 
 */
LinkComm::~LinkComm() 
{
    this->serial_.close();
}
/**
 * @brief 启动指令服务
 * 
 * @param req 接收
 * @param resp 返回
 */
bool LinkComm::StartStopCmdSrvCallBack(board_com::sscmd::Request& req, board_com::sscmd::Response& resp)
{
    if(req.cmd == 1){
        ROS_INFO("robot start");
        this->Link_SendControlCmd(1);
    }else if(req.cmd == 0){
        ROS_INFO("robot stop");
        this->Link_SendControlCmd(0);
    }
    return 1;
}
/**
 * @brief 速度指令消息订阅
 * 
 * @param msg_p 消息
 */
void LinkComm::VelCmdMsgCallBack(const geometry_msgs::Twist::ConstPtr& msg_p){
    this->velcmd_msg_.linear.x = msg_p.get()->linear.x;
    this->velcmd_msg_.linear.y = msg_p.get()->linear.y;
    this->velcmd_msg_.angular.z = msg_p.get()->angular.z * 57;
    this->Link_SendVelCmd();
}
/**
 * @brief vicon数据回调并发送
 * 
 * @param msg_p 消息
 */
void LinkComm::ViconMsgCallBack(const geometry_msgs::TransformStamped::ConstPtr& msg_p){
    Eigen::Vector3d angle;
    Eigen::Quaterniond quaternion(msg_p.get()->transform.rotation.w, msg_p.get()->transform.rotation.x, msg_p.get()->transform.rotation.y, msg_p.get()->transform.rotation.z); 
    angle = ToEulerAngles(quaternion);
    this->vicon_angle_(0) = angle(2);
    this->vicon_angle_(1) = angle(1);
    this->vicon_angle_(2) = angle(0);
    this->vicon_pos_(0) = msg_p.get()->transform.translation.x;
    this->vicon_pos_(1) = msg_p.get()->transform.translation.y;
    this->vicon_pos_(2)= msg_p.get()->transform.translation.z;
    this->Link_SendVicon();
}
/**
 * @brief led上灯带消息回调发送
 * 
 * @param msg_p 消息
 */
void LinkComm::LedUpMsgCallBack(const std_msgs::UInt32MultiArray::ConstPtr& msg_p){
    ledup_size_ = msg_p.get()->data.size();
    memcpy(this->ledupArray_, msg_p.get()->data.data(), ledup_size_ * sizeof(uint32_t));
    this->Link_LedUP();
}
/**
 * @brief led上灯带消息回调发送
 * 
 * @param msg_p 消息
 */
void LinkComm::LedDownMsgCallBack(const std_msgs::UInt32MultiArray::ConstPtr& msg_p){
    leddown_size_ = msg_p.get()->data.size();
    memcpy(this->leddownArray_, msg_p.get()->data.data(), leddown_size_ * sizeof(uint32_t));
    this->Link_LedDown();
}

/**
 * @brief led_cmd回调发送
 * 
 * @param msg_p 消息
 */
void LinkComm::LedCmdMsgCallBack(const std_msgs::Int8::ConstPtr& msg_p){
 
    ledcmd_msg = msg_p.get()->data;
    this->Link_LedCmd();
}

/**
 * @brief IMU消息发布
 * 
 */
void LinkComm::ImuMsgPublish(void){
    this->pub_imu_.publish(imu_data_msg_);
}
/**
 * @brief 电量消息发布
 * 
 */
void LinkComm::BattaryMsgPublish(void){
    battary_msg_.data = this->battery_;
    this->pub_battery_.publish(battary_msg_);
}
/**
 * @brief GPS消息发布
 * 
 */

void LinkComm::GPSMsgPublish(void){
    gps_msg_.header.frame_id = "gps_frame";
    gps_msg_.header.stamp = ros::Time::now();
    gps_msg_.longitude = this->gps_data_[0];
    gps_msg_.latitude = this->gps_data_[1];
    gps_msg_.altitude = this->gps_data_[2];
    this->pub_gps_.publish(gps_msg_);
}
/**
 * @brief 里程计消息发布
 * 
 */
void LinkComm::OdometryMsgPublish(void){
    odometry_msg_.header.frame_id = "odom_frame";
    odometry_msg_.child_frame_id = "base_link";
    odometry_msg_.header.stamp = ros::Time::now();
    odometry_msg_.pose.pose.position.x = odom_pos_(0);
    odometry_msg_.pose.pose.position.y = odom_pos_(1);
    odometry_msg_.pose.pose.position.z = 0;
    odometry_msg_.twist.twist.linear.x = odom_vel_(0);
    odometry_msg_.twist.twist.linear.y = odom_vel_(1);
    odometry_msg_.twist.twist.angular.z = odom_vel_(2);
    this->pub_odometry_.publish(odometry_msg_);
}
/**
 * @brief 获取串口数据
 * 
 */
void LinkComm::Serial_GetData(void)
{
    auto available_bytes = this->serial_.available();
    std::string str_received;
    if (available_bytes) {
        this->serial_.read(str_received, available_bytes);
        //PrintHexData(str_received);
        const char* p = str_received.data();
        for(auto i = 0;i < available_bytes; i++){
            this->Link_Receive(*(p + i));
        }
    }
}
/**
  * @brief  数据接收函数
  * @brief  帧头1 55AA 设备地址：1参考硬件定义 功能码1ID 数据长度1Len 数据内容n  数据校验2 
  * @retval 无
  */
void LinkComm::Link_Receive(uint8_t data)
{
    static uint16_t revStep = 0;
	/* 帧头 */
	if (revStep < (receive_.headLen + receive_.addrLen) &&
        data == *((uint8_t *)&receive_.head + revStep))
    {
        revStep++;
    }
	/* 接收功能码 接受完帧头与地址 */
    else if (revStep == (receive_.headLen + receive_.addrLen))
    {
        receive_.cmd = data;
        revStep++;
    }
	/* 接收数据长度 */
    else if (revStep == (receive_.headLen + receive_.addrLen) + 1)
    {
        receive_.dataLen = data;
		receive_.length = receive_.length + receive_.dataLen;
        revStep++;
    }
	/* 接收数据 数据长度位到数据最后一个字节*/
    else if (revStep > (receive_.headLen + receive_.addrLen) + 1 &&
             revStep < (receive_.headLen + receive_.addrLen) + receive_.dataLen + 2)
    {
		/* 数据位 */
        *((uint8_t *)&receive_.head + revStep) = data;
        revStep++;
    }
	/* 接收数据校验位 */
    else if (revStep == (receive_.headLen + receive_.addrLen) + receive_.dataLen + 2)
    {
        receive_.sumCheck = data;
        revStep++;
    }
	/* 接收数据校验位 */
    else if (revStep == (receive_.headLen + receive_.addrLen) + receive_.dataLen + 3)
    {
        receive_.addCheck = data;
		/* 接收完数据并解析 */
		Link_Decode();
		revStep = 0;
        receive_.dataLen = 0;
    }
	/* 无关项 */
    else
    {
        revStep = 0;
        receive_.dataLen = 0;
    }
	receive_.errorRate = (float)receive_.checkNum[1]/(receive_.checkNum[0] + receive_.checkNum[1]);
}
/**
 * @brief  解析函数 讲收到的一包数据进行解析
 */

void LinkComm::Link_Decode(void)
{
	static uint8_t sumCheck,addCheck;
	/* 记录接收校验 */
	sumCheck = receive_.sumCheck;
	addCheck = receive_.addCheck;
	/* 校验 */
	Link_Check(&receive_);
	/* 校验错误 */
	if (receive_.sumCheck != sumCheck || receive_.addCheck != addCheck)
	{
		receive_.checkNum[1]++;
        return;
	}
    if(receive_.cmd == 0x01){
        this->Link_HeartbeatDecode();
    }
    if(receive_.cmd == 0x10){
        this->Link_ImuDecode();
    }
    if(receive_.cmd == 0x11){
        this->Link_GPSDecode();
    }
    if(receive_.cmd == 0x12){
        this->Link_OdometerDecode();
    }
	/* 接收记录 */
	receive_.checkNum[0]++;
}

/**
  * @brief  校验函数
  * @retval 无
  */
void LinkComm::Link_Check(link_t *link)
{
    link->sumCheck = 0;
    link->addCheck = 0;
    for (size_t i = 0; i < 4; i++)
    {
        link->sumCheck += *(uint8_t *)(&link->head + i);
        link->addCheck += link->sumCheck;
    }

    for (size_t i = 0; i < link->dataLen; i++)
    {
        link->sumCheck += link->dataBuf[i];
        link->addCheck += link->sumCheck;
    }
}

/**
  * @brief  初始化函数
  * @retval 无
  */
void LinkComm::Link_Init(void)
{
    send_.head = SFRAME_HEAD;
	/* 暂时作为主机模式 */
    send_.addr = SFRAME_ADDR;
	send_.headLen = 1;
	send_.addrLen = 1;
	
    receive_.head = RFRAME_HEAD;	
    receive_.addr = RFRAME_ADDR;
    receive_.headLen = 1;
    receive_.addrLen = 1;
	/* 测试用 */
}
/**
  * @brief  发送循环 定时分包发送数据 
  * @retval 无
  */
void LinkComm::Link_SendLoop(void)
{
	static uint32_t tick = 0;
	tick++;
	if(tick % 500 == 0)
	{
		this->Link_Heartbeat();
	}
}
/**
  * @brief  协议发送函数
  * @brief  压入环形缓冲区等待处理
  * @retval 无
  */
void LinkComm::Link_AddSend(link_t *link)
{
	uint8_t buf[128];
	/* 获取校验数据 */
	Link_Check(link);
	/* 拷贝帧头等数据 */
	memcpy(buf, (uint8_t *)&link->head, 4);
	/* 拷贝数据 */
	memcpy(buf + 4, (uint8_t *)link->dataBuf, link->dataLen);
	/* 拷贝校验 */
	memcpy(buf + 4 + link->dataLen, (uint8_t *)&link->sumCheck, 2);
	/* 压入FIFO */
	this->serial_.write(buf, link->dataLen + 6);
}
/**
 * @brief 显示接收数据
 * 
 * @param data 数据
 */
void LinkComm::PrintHexData(const std::string &data) {
  if (!data.empty()) {
    std::cout << "data receive: ";
    for (int i = 0; i < data.size(); ++i) {
      std::cout << std::hex << std::setfill('0') << std::setw(2)
                << int(uint8_t(data.at(i))) << " ";
    }
    std::cout << std::endl;
  }
}
/**
 * @brief 发送心跳包
 * 
 */
void LinkComm::Link_Heartbeat(void)
{
    send_.cmd = 0x01;
	send_.dataLen = 2;
    this->camera_useful_ = 1;
    send_.dataBuf[0] = this->vswarm_id_;
    send_.dataBuf[1] = this->camera_useful_;
	Link_AddSend(&send_);
}
/**
 * @brief 发送启动指令
 * 
 * @param flag 开启关闭 0 1 
 */
void LinkComm::Link_SendControlCmd(bool flag)
{
    send_.cmd = 0x50;
	send_.dataLen = 1;
	send_.dataBuf[0] = flag;
    Link_AddSend(&send_);
}
/**
 * @brief 发送vicon数据
 * 
 */
void LinkComm::Link_SendVicon(void)
{
    send_.cmd = 0x18;
	send_.dataLen = 24;
    float vicon_data[6];
    vicon_data[0] = (float)vicon_angle_(0);
    vicon_data[1] = (float)vicon_angle_(1);
    vicon_data[2] = (float)vicon_angle_(2);
    vicon_data[3] = ((float)vicon_pos_(0) * 1000);
    vicon_data[4] = ((float)vicon_pos_(1) * 1000);
    vicon_data[5] = ((float)vicon_pos_(2) * 1000);
	memcpy(&send_.dataBuf[0], &vicon_data, 24);
	Link_AddSend(&send_);
}
/**
 * @brief 发送速度指令
 * 
 */
void LinkComm::Link_SendVelCmd(void)
{
	send_.cmd = 0x60;
	send_.dataLen = 12;
    float velcmd[3];
    velcmd[0] = (float)velcmd_msg_.linear.x;
    velcmd[1] = (float)velcmd_msg_.linear.y;
    velcmd[2] = (float)velcmd_msg_.angular.z;
	memcpy(&send_.dataBuf[0], &velcmd, 12);
	Link_AddSend(&send_);
}
/**
 * @brief 发送上灯带数据
 * 
 */
void LinkComm::Link_LedUP(void)
{
    send_.cmd = 0x70;
    send_.dataLen = ledup_size_ * sizeof(uint32_t);
    memcpy(&send_.dataBuf[0], &ledupArray_[0], send_.dataLen);
    Link_AddSend(&send_);  
}
/**
 * @brief 发送下灯带数据
 * 
 */
void LinkComm::Link_LedDown(void)
{
    send_.cmd = 0x71;
    send_.dataLen = leddown_size_ * sizeof(uint32_t);
    memcpy(&send_.dataBuf[0], &leddownArray_[0], send_.dataLen);
    Link_AddSend(&send_); 
}

/**
 * @brief 发送LED_CMD
 * 
 */
void LinkComm::Link_LedCmd(void)
{
    send_.cmd = 0x80;
    send_.dataLen = 2;
	memcpy(&send_.dataBuf[0], &ledcmd_msg, 2);
	Link_AddSend(&send_);
}

/**
 * @brief 解析心跳包
 * 
 */
void LinkComm::Link_HeartbeatDecode(void)
{
    this->battery_ = receive_.dataBuf[0];
    this->imu_state_ = receive_.dataBuf[1];
    this->gps_state_ = receive_.dataBuf[2];
    this->BattaryMsgPublish();
}
/**
 * @brief 解析并发布IMU话题数据
 * 
 */
void LinkComm::Link_ImuDecode(void)
{
    float fa[10];
    memcpy(&fa, &receive_.dataBuf[0], 40);
    imu_data_msg_.header.frame_id = "imu_frame";
    imu_data_msg_.header.stamp = ros::Time::now();
    imu_data_msg_.linear_acceleration.x = fa[0];
    imu_data_msg_.linear_acceleration.y = fa[1];
    imu_data_msg_.linear_acceleration.z = fa[2];
    imu_data_msg_.angular_velocity.x = fa[3];
    imu_data_msg_.angular_velocity.y = fa[4];
    imu_data_msg_.angular_velocity.z = fa[5];
    imu_data_msg_.orientation.w = fa[6];
    imu_data_msg_.orientation.x = fa[7];
    imu_data_msg_.orientation.y = fa[8];
    imu_data_msg_.orientation.z = fa[9];
    this->ImuMsgPublish();
}
/**
 * @brief 解析并发布GPS话题数据
 * 
 */
void LinkComm::Link_GPSDecode(void)
{
    float fa[4];
    double da[2];
    memcpy(&fa, &receive_.dataBuf[0], 16);
    memcpy(&da, &receive_.dataBuf[16], 16);
    mag_(0) = fa[0];
    mag_(1) = fa[1];
    mag_(2) = fa[2];  
    gps_data_(0) = da[0];
    gps_data_(1) = da[1];
    gps_data_(2) = (double)fa[3];
    this->GPSMsgPublish();
}
/**
 * @brief 解析并发布里程计话题数据
 * 
 */
void LinkComm::Link_OdometerDecode(void)
{
    float fa[5];
    memcpy(&fa, &receive_.dataBuf[0], 20);
    this->odom_vel_(0) = fa[0];
    this->odom_vel_(1) = fa[1];
    this->odom_vel_(2) = fa[2];
    this->odom_pos_(0) = fa[3];
    this->odom_pos_(1) = fa[4];
    this->OdometryMsgPublish();
}
/**
 * 
 * @brief vicon四元数转欧拉角
 * 
 * @param q 四元数
 * @return Eigen::Vector3d 欧拉角
 */
Eigen::Vector3d ToEulerAngles(Eigen::Quaterniond q) {
    Eigen::Vector3d angles;
 
    // roll (x-axis rotation)
    double sinr_cosp = 2 * (q.w() * q.x() + q.y() * q.z());
    double cosr_cosp = 1 - 2 * (q.x() * q.x() + q.y() * q.y());
    angles(2) = std::atan2(sinr_cosp, cosr_cosp);
 
    // pitch (y-axis rotation)
    double sinp = 2 * (q.w() * q.y() - q.z() * q.x());
    if (std::abs(sinp) >= 1)
        angles(1) = std::copysign(M_PI / 2, sinp); // use 90 degrees if out of range
    else
        angles(1) = std::asin(sinp);
 
    // yaw (z-axis rotation)
    double siny_cosp = 2 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = 1 - 2 * (q.y() * q.y() + q.z() * q.z());
    angles(0) = std::atan2(siny_cosp, cosy_cosp);
 
    return angles;
}
